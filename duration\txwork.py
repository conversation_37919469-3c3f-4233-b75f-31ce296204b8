import sys
import os
import requests

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


from utils.db_utils import MysqlUtil

class txwork:
    def __init__(self):
        self.url = 'https://careers.tencent.com/tencentcareer/api/post/Query?timestamp=1755570025924&countryId&cityId&bgIds&productId&categoryId&parentCategoryId&attrId&language=zh-cn&area=cn'
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        }
    def get(self, params=None, headers=None):
        ret = []
        params = {
            'keyword': 'python',
            'pageIndex': 1,
            'pageSize': 100
        }
        response = requests.get(self.url, params=params, headers=headers)
        json_data = response.json()
        total = json_data['Data']['Count']
        while len(ret) < total:
            response = requests.get(self.url, params=params, headers=headers)
            json_data = response.json()
            ret.extend(json_data['Data']['Posts'])
            params['pageIndex'] += 1
        return ret
    def parse_job_data(self, ret):
        for job in ret:
            item = dict()
            item['job_name'] = job['RecruitPostName']
            item['post_url'] = job['PostURL']
            item['description'] = job['Responsibility']
            item['city_name'] = job['CountryName']
            item['location_name'] = job['LocationName']
            self.save_job_data(item)
    def create_table(self):
        mysql_util = MysqlUtil()
        mysql_util.connect()
        sql = 'create table if not exists txwork (job_name varchar(255), post_url varchar(255), description text, city_name varchar(255), location_name varchar(255))'
        mysql_util.execute(sql)
        mysql_util.close()

    def save_job_data(self, item):
        mysql_util = MysqlUtil()
        mysql_util.connect()
        sql = 'insert into txwork (job_name, post_url, description, city_name, location_name) values (%s, %s, %s, %s, %s)'
        mysql_util.execute(sql, (item['job_name'], item['post_url'], item['description'], item['city_name'], item['location_name']))
        mysql_util.close()

if __name__ == '__main__':
    txwork = txwork()
    txwork.create_table()
    ret = txwork.get()
    txwork.parse_job_data(ret)
