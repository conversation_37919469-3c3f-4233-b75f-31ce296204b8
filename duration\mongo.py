import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from const.db_conf import *
import pymongo

# 连接MongoDB客户端
client = pymongo.MongoClient(mongo_host, mongo_port)

# 选择数据库
db = client[mongo_db]  # 使用配置文件中的数据库名 'test'

# 选择集合（表）
students_collection = db['students']

# 创建学生数据
student = {
    'id': '20170101',
    'name': 'Jordan',
    'age': 20,
    'gender': 'male',
    'major': 'Computer Science',
    'grade': 'A'
}

# 插入单个学生记录
result = students_collection.insert_one(student)
print(f"插入成功，文档ID: {result.inserted_id}")

# 插入多个学生记录
students_batch = [
    {'id': '20170102', 'name': 'Alice', 'age': 19, 'gender': 'female', 'major': 'Mathematics', 'grade': 'B'},
    {'id': '20170103', 'name': '<PERSON>', 'age': 21, 'gender': 'male', 'major': 'Physics', 'grade': 'A'},
    {'id': '20170104', 'name': '<PERSON>', 'age': 20, 'gender': 'female', 'major': 'Chemistry', 'grade': 'B+'},
    {'id': '20170105', 'name': 'David', 'age': 22, 'gender': 'male', 'major': 'Biology', 'grade': 'A-'}
]

batch_result = students_collection.insert_many(students_batch)
print(f"批量插入成功，插入了 {len(batch_result.inserted_ids)} 条记录")

# 查询所有学生
print("\n所有学生记录:")
for student in students_collection.find():
    print(f"ID: {student['id']}, 姓名: {student['name']}, 年龄: {student['age']}, 专业: {student['major']}")

# 创建索引以提高查询性能
students_collection.create_index("id", unique=True)
print("\n已为学生ID创建唯一索引")

# 显示集合统计信息
count = students_collection.count_documents({})
print(f"\n学生表中共有 {count} 条记录")