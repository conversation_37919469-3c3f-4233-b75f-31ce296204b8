import re

py_str = """Python3 高级开发工程师 上海互教教育科技有限公司上海-浦东新区2万/月02-18满员
测试开发工程师（C++/python） 上海墨鹍数码科技有限公司上海-浦东新区2.5万/每月02-18未满员
Python3 开发工程师 上海德拓信息技术股份有限公司上海-徐汇区1.3万/每月02-18剩余11人
测试开发工程师（Python） 赫里普（上海）信息科技有限公司上海-浦东新区1.1万/每月02-18剩余5人
Python高级开发工程师 上海行动教育科技股份有限公司上海-闵行区2.8万/月02-18剩余255人
python开发工程师 上海优似腾软件开发有限公司上海-浦东新区2.5万/每月02-18满员"""

if __name__ =='__main__':
  # ret = re.findall(r'([\d.]+)万/每{0,1}月',py_str)
  # print(ret)

  source = """张三，手机号码15945678901
李四，手机号码13945677701
王二，手机号码13845666901
"""

  source2 = """
<div class="el">
        <p class="t1">           
            <span>
                <a>Python开发工程师</a>
            </span>
        </p>
        <span class="t2">南京</span>
        <span class="t3">1.5-2万/月</span>
</div>
<div class="el">
        <p class="t1">
            <span>
                <a>java开发工程师</a>
            </span>
		</p>
        <span class="t2">苏州</span>
        <span class="t3">1.5-2/月</span>
</div>
"""


  # for temp in re.findall(r'^(.*)，.+(\d{11})', source,re.M):
  #     print(temp)
  for temp in re.findall(r'class=\"t1\">.*?<a>(.*?)</a>',source2,re.DOTALL):
    print(temp)