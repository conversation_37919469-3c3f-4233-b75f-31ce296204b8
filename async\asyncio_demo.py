import asyncio
import requests
from bs4 import BeautifulSoup
from functools import partial

loop = asyncio.get_event_loop()

async def get_movie_info(page):
    url = "https://movie.douban.com/top250?start={}&filter"
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36"
    }
    resp = await loop.run_in_executor(None, partial(requests.get, url.format(page * 25), headers=headers))
    resp = requests.get(url, headers=headers)
    soup = BeautifulSoup(resp.text, "lxml")
    movie_list = soup.find_all("div", class_="hd")
    print(page)
    # for movie in movie_list:
        # print(movie.get_text())
    # print(soup)

# asyncio.run(get_movie_info())
if __name__ == '__main__':
    task = [loop.create_task(get_movie_info(page)) for page in range(10)]
    loop.run_until_complete(asyncio.wait(task))