# -*- coding:utf-8 -*-
from pathlib import Path
from typing import Union, Any, Optional, List, Tuple

from .base import OriginalRecorder, BaseRecorder
from .style import CellStyle
from .db_recorder import DBRecorder
from .filler import Filler
from .recorder import Recorder


class OriginalSetter(object):
    _recorder: OriginalRecorder = ...

    def __init__(self, recorder: OriginalRecorder): ...

    def cache_size(self, size: int) -> OriginalSetter: ...

    def path(self, path: Union[str, Path]) -> OriginalSetter: ...

    def show_msg(self, on_off: bool) -> OriginalSetter: ...

    def auto_backup(self,
                    interval: int = None,
                    path: Union[str, Path] = None,
                    new_name: bool = None) -> OriginalSetter: ...


class BaseSetter(OriginalSetter):
    _recorder: BaseRecorder = ...

    def table(self, name: Union[str, bool]) -> BaseSetter: ...

    def before(self, before: Any) -> BaseSetter: ...

    def after(self, after: Any) -> BaseSetter: ...

    def encoding(self, encoding: str) -> BaseSetter: ...


class SheetLikeSetter(BaseSetter):
    _recorder: Union[Filler, Recorder] = ...

    def head(self, head: Union[list, tuple], table: str = None, to_file: bool = True) -> SheetLikeSetter: ...

    def delimiter(self, delimiter: str) -> SheetLikeSetter: ...

    def quote_char(self, quote_char: str) -> SheetLikeSetter: ...

    def path(self, path: Union[str, Path], file_type: str = None) -> SheetLikeSetter: ...

    def file_type(self, file_type: str) -> SheetLikeSetter: ...

    def table(self, name: Union[str, bool]) -> SheetLikeSetter: ...

    def head_row(self, num: int) -> SheetLikeSetter: ...

    def fit_head(self, on_off: bool = True) -> SheetLikeSetter: ...


class FillerSetter(SheetLikeSetter):
    _recorder: Filler = ...

    def __init__(self, recorder: Filler): ...

    # -------------------上级开始-------------------

    def cache_size(self, size: int) -> FillerSetter: ...

    def show_msg(self, on_off: bool) -> FillerSetter: ...

    def auto_backup(self,
                    interval: int = None,
                    path: Union[str, Path] = None,
                    new_name: bool = None) -> FillerSetter: ...

    def table(self, name: Union[str, bool]) -> FillerSetter: ...

    def before(self, before: Any) -> FillerSetter: ...

    def after(self, after: Any) -> FillerSetter: ...

    def encoding(self, encoding: str) -> FillerSetter: ...

    def head(self, head: Union[list, tuple], table: str = None, to_file: bool = True) -> FillerSetter: ...

    def delimiter(self, delimiter: str) -> FillerSetter: ...

    def quote_char(self, quote_char: str) -> FillerSetter: ...

    def file_type(self, file_type: str) -> FillerSetter: ...

    def head_row(self, num: int) -> FillerSetter: ...

    # -------------------上级结束-------------------

    def sign(self, value: Any) -> FillerSetter: ...

    def deny_sign(self, on_off: bool = True) -> FillerSetter: ...

    def key_cols(self, cols: Union[str, int, list, tuple, bool]) -> FillerSetter: ...

    def sign_col(self, col: Union[str, int, bool]) -> FillerSetter: ...

    def data_col(self, col: Union[str, int]) -> FillerSetter: ...

    def begin_row(self, row: int) -> FillerSetter: ...

    def path(self,
             path: Union[str, Path] = None,
             key_cols: Union[str, int, list, tuple, bool] = None,
             begin_row: int = None,
             sign_col: Union[str, int, bool] = None,
             data_col: Union[str, int] = None,
             sign: Any = None,
             deny_sign: bool = None) -> FillerSetter: ...

    def link_style(self, style: CellStyle) -> FillerSetter: ...


class RecorderSetter(SheetLikeSetter):
    _recorder: Recorder = ...

    def __init__(self, recorder: Recorder): ...

    # -------------------上级开始-------------------

    def cache_size(self, size: int) -> RecorderSetter: ...

    def show_msg(self, on_off: bool) -> RecorderSetter: ...

    def auto_backup(self,
                    interval: int = None,
                    path: Union[str, Path] = None,
                    new_name: bool = None) -> RecorderSetter: ...

    def table(self, name: Union[str, bool]) -> RecorderSetter: ...

    def before(self, before: Any) -> RecorderSetter: ...

    def after(self, after: Any) -> RecorderSetter: ...

    def encoding(self, encoding: str) -> RecorderSetter: ...

    def head(self, head: Union[list, tuple], table: str = None, to_file: bool = True) -> RecorderSetter: ...

    def delimiter(self, delimiter: str) -> RecorderSetter: ...

    def quote_char(self, quote_char: str) -> RecorderSetter: ...

    def file_type(self, file_type: str) -> RecorderSetter: ...

    def head_row(self, num: int) -> RecorderSetter: ...

    # -------------------上级结束-------------------

    def follow_styles(self, on_off: bool = True) -> RecorderSetter: ...

    def col_height(self, height: float) -> RecorderSetter: ...

    def styles(self, styles: Union[CellStyle, List[CellStyle], Tuple[CellStyle], None]) -> RecorderSetter: ...

    def path(self, path: Union[str, Path], file_type: str = None) -> RecorderSetter: ...

    def fit_head(self, on_off: bool = True, add_new: bool = False) -> RecorderSetter: ...


class DBSetter(BaseSetter):
    _recorder: DBRecorder = ...

    def __init__(self, recorder: DBRecorder): ...

    # -------------------上级开始-------------------

    def cache_size(self, size: int) -> DBSetter: ...

    def show_msg(self, on_off: bool) -> DBSetter: ...

    def auto_backup(self,
                    interval: int = None,
                    path: Union[str, Path] = None,
                    new_name: bool = None) -> DBSetter: ...

    def table(self, name: Union[str, bool]) -> DBSetter: ...

    def before(self, before: Any) -> DBSetter: ...

    def after(self, after: Any) -> DBSetter: ...

    def encoding(self, encoding: str) -> DBSetter: ...

    # -------------------上级结束-------------------

    def path(self, path: Union[str, Path], table: Optional[str] = None) -> DBSetter: ...


def set_csv_head(recorder: Union[Recorder, Filler],
                 head: Union[list, tuple],
                 to_file: bool,
                 row: int = 1) -> None: ...


def set_xlsx_head(recorder: Union[Recorder, Filler],
                  head: Union[list, tuple],
                  table: str,
                  to_file: bool,
                  row: int = 1) -> None: ...
