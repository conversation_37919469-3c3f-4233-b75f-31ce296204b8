import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import requests
from utils.db_utils import MysqlUtil

class aliWork:
  def __init__(self):
    self.url = 'https://talent.taotian.com/position/search?_csrf=a32b2d1c-45f5-421b-ad58-31129fe26b5f'
    self.headers = {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'Content-Type': 'application/json',
      'Cookie': 'XSRF-TOKEN=a32b2d1c-45f5-421b-ad58-31129fe26b5f; prefered-lang=zh; SESSION=Qzk2NkNBM0QzNzRFQzMzMjBGMTVGRERFMUExNTUwMTQ=; cna=CQUrId3ADAMCAQ6ZOGMg5O7r; xlly_s=1; tfstk=gvurqg0ooULrPLWXPxUFQdj65A48kyJ1r2wQtXc3N82ud3dF8xlgxwt8PWYEdxZnPzGS3vkZCu0W2bGh-X2Yd8wSVWYEnxZuR0w7t_3qQw_We3hh8yaHCdT65bd8Jyv1hjNXdTF___YQq720yWMnQgmB5bhRi_WH1mY1T5r4uW2nKuq0m7w0ZMqoKrc0s-butaVhnIPLnyVuqk40nWNzZMboKjA49-23-uD3oIPLnJ43-bqtZXb41uADZGQnMZfEsRcu3wbN6-ZldxsCPag4auung-PbGqPz4RZHzGIqrxGnP4EvmizKGckExAvfV8ma_xrs4d7rQYFnTWDyW1ymzXu42m1OSjrUUm0u06bnabwECDkyUgeoVxrTiofeWSM_nb3o0BBmZAwzrSqXSCl0xmg8X49czPmtNzinIKs7EcPh4U6LirsxJ0WhYuVYgRO2ggUrKplezyq5vME0WSy6N7sdvuVYgRO2gMILm5F4CQN5.; isg=BAYG4f7ZkrVSFkbRN5VKb8_GV_yIZ0oh5A0GG_AktCku86wNT_W1MXXFyy8_20I5'
    }
    self.params = {
      "channel": "group_official_site",
      "language": "zh",
      "batchId": "",
      "deptCodes": [],
      "pageIndex": 1,
      "pageSize": 100,
      "regions": "",
      "shareType": "",
      "shareId": "",
      "myReferralShareCode": ""
    }
  def get(self):
    response = requests.post(self.url, json=self.params, headers=self.headers)
    return response
  
  def parse_job_data(self):
    ret = []
    while True:
      response = self.get()
      json_data = response.json()
      ret.extend(json_data['content']['datas'])
      if json_data['content']['totalCount'] > len(ret):
        self.params['pageIndex'] += 1
      else:
        break
    for job in ret:
      item = dict()
      item['job_name'] = job['name']
      item['post_url'] = job['positionUrl']
      item['description'] = job['description']
      item['city_name'] = job['workLocations'][0]
      item['requirement'] = job['requirement']
      self.save_job_data(item)
  def create_table(self):
    mysql_util = MysqlUtil()
    mysql_util.connect()
    sql = 'create table if not exists aliwork (job_name varchar(255), post_url varchar(255), description text, city_name varchar(255), requirement text )'
    mysql_util.execute(sql)
    mysql_util.close()
  def save_job_data(self, item):
    mysql_util = MysqlUtil()
    mysql_util.connect()
    sql = 'insert into aliwork (job_name, post_url, description, city_name, requirement) values (%s, %s, %s, %s, %s)'
    mysql_util.execute(sql, (item['job_name'], item['post_url'], item['description'], item['city_name'], item['requirement']))
    mysql_util.close()
if __name__ == '__main__':
  # resp = aliWork().get()
  aliWork().create_table()
  aliWork().parse_job_data()
  # print(len(resp))