import requests
import pymongo


class AiQiYi:
  def __init__(self):
    self.client = pymongo.MongoClient('192.168.0.53', 27017)
    self.db = self.client['test']
    self.collection = self.db['videos']
    self.headers = {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    }
    self.url = 'https://pcw-api.iqiyi.com/search/recommend/list?channel_id=2&data_type=1&mode=11'


  def get_video_info(self):
    resp = requests.get(url=self.url, headers=self.headers)
    # print(resp.json())
    return resp

  def parse_video_info(self,resp):
    # title,play_url,description
    resp_data = resp.json()
    video_list = resp_data['data']['list']
    for video in video_list:
      item = dict()
      item['title'] = video['name']
      item['playUrl'] = video['playUrl']
      item['description'] = video['description']
      self.save_video_info(item)

  def save_video_info(self,item):
    self.collection.insert_one(item)
    
  def main(self):
    resp = self.get_video_info()
    self.parse_video_info(resp)

if __name__ == '__main__':
  aqy = AiQiYi()
  aqy.main()
