import asyncio
import requests
import aiohttp
from bs4 import BeautifulSoup, Tag
import time


async def get_data(session, page):
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36"
    }
    url = 'https://movie.douban.com/top250?start={}&filter='
    print(f"开始请求第 {page} 页: {url.format(page * 25)}")
    
    try:
        async with session.get(url.format(page * 25), headers=headers) as resp:
            html = await resp.text()
            soup = BeautifulSoup(html, "lxml") 
            movie_list = soup.find_all("div", class_="hd")
            titles = []
            for movie in movie_list:
                if isinstance(movie, Tag):
                    title_spans = movie.find_all("span", class_="title")
                    if title_spans:
                        titles.append(title_spans[0].get_text())
            print(f"第 {page} 页完成，获取到 {len(titles)} 部电影")
            return titles
    except Exception as e:
        print(f"第 {page} 页请求失败: {e}")
        return []
  


async def async_main_timed():
    print("异步版本开始:", time.strftime("%X"))
    start_time = time.time()
    
    async with aiohttp.ClientSession() as session:
        async with asyncio.TaskGroup() as tg:
            tasks = [tg.create_task(get_data(session, page)) for page in range(10)]
    
    end_time = time.time()
    print(f"异步版本完成: {time.strftime('%X')}, 耗时: {end_time - start_time:.2f}秒")


def get_data_sync(page):
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36"
    }
    url = 'https://movie.douban.com/top250?start={}&filter='
    print(f"同步请求第 {page} 页: {url.format(page * 25)}")
    
    try:
        resp = requests.get(url.format(page * 25), headers=headers)
        soup = BeautifulSoup(resp.text, "lxml") 
        movie_list = soup.find_all("div", class_="hd")
        titles = []
        for movie in movie_list:
            if isinstance(movie, Tag):
                title_spans = movie.find_all("span", class_="title")
                if title_spans:
                    titles.append(title_spans[0].get_text())
        print(f"同步第 {page} 页完成，获取到 {len(titles)} 部电影")
        return titles
    except Exception as e:
        print(f"同步第 {page} 页请求失败: {e}")
        return []
def sync_main():
    print("同步版本开始:", time.strftime("%X"))
    start_time = time.time()
    
    for i in range(10):
        get_data_sync(i)
    
    end_time = time.time()
    print(f"同步版本完成: {time.strftime('%X')}, 耗时: {end_time - start_time:.2f}秒")

if __name__ == '__main__':
    print("=== 性能对比测试 ===")
    
    # 测试同步版本
    sync_main()
    
    print("\n" + "="*50 + "\n")
    
    # 测试异步版本
    asyncio.run(async_main_timed())
