import asyncio
import time

# 协程顺序执行
# async def main():
#   print(f"started at {time.strftime('%X')}")
#   await say_after(1, 'hello')
#   await say_after(2, 'world')
#   print(f"finished at {time.strftime('%X')}")

# 协程并发执行
# async def main():
#   task1 = asyncio.create_task(say_after(1, 'hello'))
#   task2 = asyncio.create_task(say_after(2, 'world'))
#   print(f"started at {time.strftime('%X')}")
#   await task1
#   await task2
#   print(f"finished at {time.strftime('%X')}")

async def main():
  async with asyncio.TaskGroup() as tg:
    task  = tg.create_task(say_after(1, 'hello'))
    task2 = tg.create_task(say_after(2, 'world'))
    print(f"started at {time.strftime('%X')}")
  print(f"finished at {time.strftime('%X')}")

async def say_after(delay, what):
  await asyncio.sleep(delay)
  print(what)

async def nested():
  return 42

async def main2():
  print(await nested())



if __name__ == '__main__':
  # asyncio.run(main())
  asyncio.run(main2())