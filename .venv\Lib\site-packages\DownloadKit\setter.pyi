# -*- coding:utf-8 -*-
"""
<AUTHOR>   g1879
@Contact :   <EMAIL>
"""
from pathlib import Path
from typing import Union, Literal, Optional

from DrissionPage import SessionOptions
from DrissionPage._base.base import BasePage
from requests import Session

from .downloadKit import DownloadKit

FILE_EXISTS = Literal['add', 'skip', 'rename', 'overwrite', 'a', 's', 'r', 'o']


class Setter(object):
    _downloadKit: DownloadKit = ...

    def __init__(self, downloadKit: DownloadKit): ...

    @property
    def if_file_exists(self) -> FileExists: ...

    @property
    def log(self) -> LogSet: ...

    def driver(self, driver: Union[Session, BasePage, SessionOptions]) -> None: ...

    def roads(self, num: int) -> None: ...

    def retry(self, times: int) -> None: ...

    def interval(self, seconds: float) -> None: ...

    def timeout(self, seconds: float) -> None: ...

    def save_path(self, path: Union[str, Path]) -> None: ...

    def split(self, on_off: bool) -> None: ...

    def block_size(self, size: Union[str, int]) -> None: ...

    def proxies(self, http: str = None, https: str = None) -> None: ...

    def encoding(self, encoding: Optional[str]) -> None: ...


class LogSet(object):
    _setter: Setter = ...

    def __init__(self, setter: Setter): ...

    def path(self, path: Union[str, Path]) -> None: ...

    def print_all(self) -> None: ...

    def print_failed(self) -> None: ...

    def print_nothing(self) -> None: ...

    def log_all(self) -> None: ...

    def log_failed(self) -> None: ...

    def log_nothing(self) -> None: ...


class FileExists(object):
    _setter: Setter = ...

    def __init__(self, setter: Setter): ...

    def __call__(self, mode: FILE_EXISTS): ...

    def skip(self) -> None: ...

    def rename(self) -> None: ...

    def overwrite(self) -> None: ...
